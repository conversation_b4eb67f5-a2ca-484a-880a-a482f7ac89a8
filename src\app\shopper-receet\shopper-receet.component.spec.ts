import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { ShopperReceetComponent } from './shopper-receet.component';

describe('ShopperReceetComponent', () => {
  let component: ShopperReceetComponent;
  let fixture: ComponentFixture<ShopperReceetComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ShopperReceetComponent,
        FormsModule,
        TopNavbarComponent,
        SidebarComponent
      ]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(ShopperReceetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should sort transactions by date (newest first)', () => {
    // Mock transactions with different dates
    const mockTransactions = [
      { ticketNumber: 1, date: '01/01/2023', productName: 'Product 1', brandName: 'Brand A' },
      { ticketNumber: 2, date: '15/06/2024', productName: 'Product 2', brandName: 'Brand B' },
      { ticketNumber: 3, date: '10/03/2024', productName: 'Product 3', brandName: 'Brand C' },
      { ticketNumber: 4, date: '25/12/2023', productName: 'Product 4', brandName: 'Brand D' }
    ];

    // Set the transactions
    component.transactions = mockTransactions;

    // Call filterTransactions which includes sorting
    component.filterTransactions();

    // Check that transactions are sorted by date (newest first)
    expect(component.filteredTransactions[0].date).toBe('15/06/2024'); // Newest
    expect(component.filteredTransactions[1].date).toBe('10/03/2024');
    expect(component.filteredTransactions[2].date).toBe('25/12/2023');
    expect(component.filteredTransactions[3].date).toBe('01/01/2023'); // Oldest
  });

  it('should parse transaction dates correctly', () => {
    // Test the private parseTransactionDate method through reflection
    const parseDate = (component as any).parseTransactionDate.bind(component);

    const date1 = parseDate('15/06/2024');
    expect(date1).toEqual(new Date(2024, 5, 15)); // Month is 0-indexed

    const date2 = parseDate('01/01/2023');
    expect(date2).toEqual(new Date(2023, 0, 1));

    const invalidDate = parseDate('invalid-date');
    expect(invalidDate).toBeNull();
  });
});